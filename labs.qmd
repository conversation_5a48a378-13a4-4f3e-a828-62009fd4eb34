---
title: "Labs"
---

Labs are hands-on sessions designed to reinforce lecture concepts using R.

- **Lab 0 (Sep 3):** Installing R and RStudio, packages, reading data  
- **Lab 1 (Sep 10):** Measures of central tendency and dispersion in R  
- **Lab 2 (Sep 17):** Data structures in R  
- **Lab 3 (Sep 24):** Recoding and cleaning data  
- **Lab 4 (Oct 1):** Simulating DGP I  
- **Lab 5 (Oct 8):** Simulating DGP II  
- **Lab 6 (Oct 15):** Review and Q&A  
- **Lab 7 (Oct 29):** Statistical tests and ANOVA in R  
- **Lab 8 (Nov 5):** Correlation in R  
- **Lab 9 (Nov 12):** Linear regression in R  
- **Lab 10 (Nov 19):** GLMs in R  
- **Lab 11 (Dec 3):** Data visualization in R  
- **Lab 12 (Dec 10):** Review and Q&A
