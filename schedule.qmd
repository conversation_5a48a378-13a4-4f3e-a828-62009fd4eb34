---
title: "Schedule"
---

The course meets **Tuesday & Thursday 9:05 AM–10:20 AM** for lectures and **Wednesday 9:05 AM–9:55 AM** for labs at the rooms listed on the Home page. Dates are listed as provided in the syllabus. Any updates will be posted here.

## Module 1: Data and Social Research
- **Aug 26:** Data and Society  
- **Aug 27:** _No Lab This Week_  
- **Aug 28:** Quantitative Social Research  
- **Sep 2:** Quantitative Research Toolbox  
- **Sep 3 (Lab 0):** Obtaining R and RStudio, installing packages, reading data  
- **Sep 4:** Research Design: Population, Sample, and Sampling  
- **Sep 9:** Measures of Central Tendency and Dispersion  
- **Sep 10 (Lab 1):** Measures of Central Tendency and Dispersion in R  
- **Sep 11 (Seminar I):** Quantitative Social Research as a Career  
- **Mini Project I:** Draft an outline of a Quantitative Research Proposal

## Module 2: The Data Generation Process
- **Sep 16:** Data Generation Process (DGP) I  
- **Sep 17 (Lab 2):** Data structures in R  
- **Sep 18:** Data Generation Process (DGP) II  
- **Sep 23:** Central Limit Theorem  
- **Sep 24 (Lab 3):** Recoding and cleaning data  
- **Sep 25:** Systematic Component of the DGP I  
- **Sep 30:** Systematic Component of the DGP II  
- **Oct 1 (Lab 4):** Simulating DGP I  
- **Oct 2:** Stochastic Component of the DGP I  
- **Oct 7:** Stochastic Component of the DGP II  
- **Oct 8 (Lab 5):** Simulating DGP II  
- **Oct 9:** Estimating Uncertainty I  
- **Oct 14:** Estimating Uncertainty II  
- **Oct 15 (Lab 6):** Review and Q&A  
- **Mini Project II:** Simulating a Probability Distribution

## Module 3: Translating Data into Insights
- **Oct 16:** Hypothesis Testing I  
- **Oct 21:** Hypothesis Testing II  
- **Oct 22:** _No Lab_  
- **Oct 23:** Comparing Two Means  
- **Oct 28:** Analysis of Variance  
- **Oct 29 (Lab 7):** Statistical Tests and ANOVA in R  
- **Oct 30:** Correlation  
- **Nov 4:** What is Regression  
- **Nov 5 (Lab 8):** Correlation in R  
- **Nov 6:** Linear Regression I  
- **Nov 11:** Linear Regression II  
- **Nov 12 (Lab 9):** Linear Regression in R  
- **Nov 13:** Generalized Linear Regression I  
- **Nov 18:** Generalized Linear Regression II  
- **Nov 19 (Lab 10):** GLMs in R  
- **Mini Project III:** Run a Linear Regression

## Module 5: Presenting Your Research
- **Nov 20:** Data Visualization I  
- **Dec 2:** Data Visualization II  
- **Dec 3 (Lab 11):** Data Visualization in R  
- **Dec 4:** Communicating Research Results with Stakeholders  
- **Dec 9 (Seminar II):** Research for Social Good  
- **Dec 10 (Lab 12):** Review and Q&A  
- **Dec 11:** Putting All Things Together
