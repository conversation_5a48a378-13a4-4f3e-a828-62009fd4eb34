name: Quarto Publish

on:
  push:
    branches: [ "main" ]
  workflow_dispatch:

permissions:
  contents: write
  pages: write
  id-token: write

jobs:
  build-deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Check out
        uses: actions/checkout@v4

      - name: Set up R
        uses: r-lib/actions/setup-r@v2

      - name: Set up Quarto
        uses: quarto-dev/quarto-actions/setup@v2

      - name: Install R packages
        run: |
          Rscript -e 'install.packages(c("readr","dplyr","gt"), repos="https://cloud.r-project.org")'

      - name: Render and Publish to GitHub Pages (gh-pages)
        uses: quarto-dev/quarto-actions/publish@v2
        with:
          target: gh-pages
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
