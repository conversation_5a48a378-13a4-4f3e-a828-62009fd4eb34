---
title: "Syllabus"
---

# Course Description
**SOC 470: Intermediate Social Statistics** is Penn State's capstone statistics course designed for social science majors. This practical course teaches you to think statistically and solve problems quantitatively.

**Topics include:**
- How to answer a question quantitatively  
- Basic probability and probability distributions  
- Basic statistical inference  
- How to present your quantitative research results

The course emphasizes hands-on data analysis using **R** with real-world social science datasets. You will master the entire quantitative social research pipeline, from data wrangling to modeling to effective communication of results.

Designed for non-STEM students, this course only requires basic statistical knowledge from SOC 207 or an equivalent introductory research methods class. No prior programming experience is needed.

**Overarching goals:**
1. Inspire you to explore unfamiliar waters without fear of failure.  
2. Develop strong data literacy and statistical thinking skills.  
3. Gain practical experience with industry-standard tools and methods.

By the end of the course, you will have the confidence and skills to tackle data-driven problems and to compete for roles requiring data analysis and interpretation.

# Pedagogy Behind This Course
**Accessible.** Not everyone enters a statistics class with a solid background in math or prior statistics knowledge. This course is designed so that regardless of your prior knowledge, you will understand the material and learn to apply it in your job, research, and life.

**Useful.** From curriculum design to evaluation, this course is structured to help you learn how to analyze data in real-world settings and build artifacts that support your thesis, honor project, graduate school applications, or job search.

**Supportive.** There are no traditional midterm or final exams. Instead, you will complete multiple quizzes, mini projects, and a final capstone. Quizzes provide timely feedback, and projects let you find your own path to success.

# What Makes This Course Different
In practice, you will not be asked to compute probabilities or matrix algebra by hand. You will be asked to:
- Turn a general question into a quantitatively answerable one,
- Select sound, defensible methods,
- Analyze data with modern software,
- Present results to stakeholders clearly.

We will build the ability to **ask the right question, choose the right tools, and use them well**.

# Learning Objectives
Through lectures, quizzes, and three hands-on mini projects, you will learn:
- The complete quantitative research pipeline,
- How to turn a social science question into a data science project,
- How to translate data into insights,
- How to present findings effectively and professionally.

You will complete a capstone project that doubles as a research proposal useful for theses, recruiting, and graduate school applications.

# Prerequisites and Materials
- No prior programming required; basic statistics and research methods helpful.
- No textbook required. Required and optional readings will be posted on the course site.
- We will use **R** to explore, analyze, and visualize data. You will write simple, clear code to run analyses; focus is on interpretation and reporting.

Welcome to social statistics—the craft of turning curiosity into rigorous scientific inquiry.

# Evaluation & Grading
## Evaluation components
- **In-Class Quizzes (6):** Canvas-based, ~20 minutes each, multiple choice, with immediate feedback and live discussion.
- **Mini Projects (3):** Guided applications of key concepts; tightly aligned to lectures, sections, and labs.
- **Capstone:** A six-page (double-spaced) research proposal suitable for showcasing on LinkedIn or as a starting point for advanced research.

## Grading composition
| Evaluation Item                  | Proportion |
|----------------------------------|-----------:|
| In-Class Quizzes                 | 30%        |
| Mini Project I                   | 10%        |
| Mini Project II                  | 10%        |
| Mini Project III                 | 10%        |
| Capstone: Research Proposal      | 40%        |

# University Policies
See the **Policies** page for required statements and resources:
- Disability Accommodation Statement  
- Academic Integrity Statement  
- Counseling and Psychological Services  
- Educational Equity / Report Bias

> Instructor: **Siyang Ni, M.Phil.** · Departments of Sociology and Criminology & Center for Social Data Analytics · **Office:** Welch Building 526 (5th Floor) · **Email:** <<EMAIL>>.
